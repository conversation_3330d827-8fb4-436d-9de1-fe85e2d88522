import { defineStore } from 'pinia'
import { login, logout } from '@/api/auth'
import { api } from '@/utils/request'

// getInfo API
const getInfo = () => {
  return api.get('/api/auth/getInfo')
}

export const useUserStore = defineStore('user', {
  state: () => ({
    userId: undefined,
    token: undefined,
    name: '',
    email: '',
    mp_open_id: undefined,
    roles: [],
    permissions: [],
  }),
  actions: {
    isLogin() {
      return new Promise((resolve, reject) => {
        if (this.token) {
          // 这里可以调用后端验证token的接口
          // 暂时简化为直接返回true
          resolve(true)
        } else {
          resolve(false)
        }
      })
    },
    login(userInfo) {
      const email = userInfo.email.trim()
      const { password } = userInfo
      return new Promise((resolve, reject) => {
        login({ email, password })
          .then((res) => {
            this.token = res.data.token
            this.$persist()
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    getUserInfo() {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const { user } = res.data
            if (res.data.roles && res.data.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.data.roles
              this.permissions = res.data.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.userId = user.id
            this.name = user.username
            this.email = user.email
            this.mp_open_id = user.mp_open_id
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    logout() {
      return new Promise((resolve, reject) => {
        logout()
          .then(() => {
            this.token = undefined
            this.userId = undefined
            this.name = ''
            this.email = ''
            this.mp_open_id = undefined
            this.roles = []
            this.permissions = []
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

  },
  persist: {
    storage: localStorage,
    key: 'Admin-Token',
    pick: ['token'],
  },
})
